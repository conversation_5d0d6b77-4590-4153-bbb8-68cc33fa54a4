import math
import numpy as np
from PIL import Image, ImageDraw, ImageFont

# --- HELPER FUNCTIONS ---
def _bilinear_sample(arr, xs, ys):
  """
  Bilinear sample of 2D array `arr` at floating coords (xs, ys).
  xs, ys are 1D arrays of same length.
  Returns 1D array of sampled values.
  """
  h, w = arr.shape
  x0 = np.floor(xs).astype(np.int32)
  y0 = np.floor(ys).astype(np.int32)
  x1 = np.clip(x0 + 1, 0, w - 1)
  y1 = np.clip(y0 + 1, 0, h - 1)

  x0 = np.clip(x0, 0, w - 1)
  y0 = np.clip(y0, 0, h - 1)

  dx = xs - x0
  dy = ys - y0

  Ia = arr[y0, x0]
  Ib = arr[y0, x1]
  Ic = arr[y1, x0]
  Id = arr[y1, x1]

  top = Ia * (1 - dx) + Ib * dx
  bot = Ic * (1 - dx) + Id * dx
  return top * (1 - dy) + bot * dy


def _unwrap_angle(a):
  """Map angle to (-pi, pi]."""
  return (a + np.pi) % (2 * np.pi) - np.pi


def _interp_crossing(a0, v0, a1, v1, v_target):
  """
  Linear interpolation of the angle where v crosses v_target between (a0,v0) and (a1,v1).
  Angles are assumed already unwrapped (monotonic segment).
  """
  t = (v_target - v0) / (v1 - v0 + 1e-12)
  return a0 + t * (a1 - a0)


def _compute_extended_field(cx, cy, ring_radius, radiators, lamb_px):
  """
  Compute intensity field in an extended region around (cx, cy) to accommodate
  far-field sampling at the specified ring radius.

  This function is called when the requested far-field sampling radius (FARFIELD)
  extends beyond the boundaries of the main intensity calculation. It ensures
  that antenna parameters (HPBW, F/B ratio, directivity) are calculated at a
  consistent electrical distance from the radiators, regardless of the output
  image size.

  Args:
    cx, cy: center coordinates in the original coordinate system
    ring_radius: required sampling radius in pixels
    radiators: dictionary of radiator configurations
    lamb_px: wavelength in pixels

  Returns:
    I_extended: 2D intensity array covering the extended region
    offset_x, offset_y: offsets to map original coordinates to extended field
  """
  # Calculate required field size to accommodate the sampling ring
  margin = int(ring_radius * 1.1)  # Add 10% margin for safety
  field_size = 2 * margin + 1

  # Calculate offsets to center the original coordinates in the extended field
  offset_x = margin - cx
  offset_y = margin - cy

  # Create coordinate grids for the extended field
  y_ext, x_ext = np.mgrid[0:field_size, 0:field_size]

  # Adjust coordinates to match the original coordinate system
  x_ext = x_ext - offset_x
  y_ext = y_ext - offset_y

  # Compute field using the same method as the main calculation
  K = 2.0 * np.pi / lamb_px
  E_real = np.zeros((field_size, field_size), dtype=np.float64)
  E_imag = np.zeros((field_size, field_size), dtype=np.float64)

  for r in radiators.values():
    dx = x_ext - r["x"]
    dy = y_ext - r["y"]
    dist = np.hypot(dx, dy)
    phase_total = K * dist + np.deg2rad(r.get("phase", 0.0))
    amp = r.get("amp", 1.0)
    E_real += amp * np.cos(phase_total)
    E_imag += amp * np.sin(phase_total)

  # Calculate intensity
  I_extended = E_real**2 + E_imag**2

  # Normalize by theoretical max (sum of amplitudes)^2
  amp_sum = sum(r.get("amp", 1.0) for r in radiators.values())
  I_extended /= (amp_sum ** 2 + 1e-12)
  I_extended = np.clip(I_extended, 0.0, 1.0)

  return I_extended, offset_x, offset_y



def estimate_hpbw(I, cx, cy, lamb_px, ring_choice_px=None, num_samples=2048, threshold_db=3.0, omni_threshold=0.1):
  """
  Estimate HPBW by sampling intensity on a far-field ring around (cx, cy).

  Args:
    I: 2D intensity array
    cx, cy: center coordinates
    lamb_px: wavelength in pixels
    ring_choice_px: optional ring radius in pixels (uses FARFIELD if None)
    num_samples: number of angular samples around the ring
    threshold_db: threshold in dB below peak for beamwidth calculation
    omni_threshold: maximum variation (as fraction of peak) to consider omnidirectional

  Returns (theta_left, theta_right, hpbw_rad, theta_peak, is_omnidirectional).
  For omnidirectional patterns, returns (None, None, 2*pi, None, True).
  """
  # Use fixed far-field radius independent of image size for consistent results
  if ring_choice_px is None:
    ring = int(FARFIELD)
  else:
    ring = int(ring_choice_px)

  angles = np.linspace(-np.pi, np.pi, num_samples, endpoint=False)
  xs = cx + ring * np.cos(angles)
  ys = cy + ring * np.sin(angles)
  I_theta = _bilinear_sample(I, xs, ys)

  # Peak and threshold level
  idx_peak = int(np.argmax(I_theta))
  I_peak = I_theta[idx_peak]
  I_min = np.min(I_theta)

  # Check for omnidirectional pattern (uniform intensity around the ring)
  intensity_variation = (I_peak - I_min) / (I_peak + 1e-12)
  if intensity_variation < omni_threshold:
    # Omnidirectional pattern detected
    return None, None, 2.0 * np.pi, None, True

  # Convert dB threshold to linear scale: threshold_linear = 10^(-threshold_db/10)
  threshold_linear = 10.0 ** (-threshold_db / 10.0)
  threshold_level = threshold_linear * I_peak

  # Search to the "left" of the peak (decreasing index, wrap) for first crossing
  def find_cross(start, step):
    i0 = start
    for _ in range(num_samples):
      i1 = (i0 + step) % num_samples
      v0, v1 = I_theta[i0], I_theta[i1]
      if (v0 - threshold_level) * (v1 - threshold_level) <= 0.0:  # crossed or touched
        # unwrap local angles for interpolation
        a0 = angles[i0]
        a1 = angles[i1]
        # make a1 close to a0 (handle wrap)
        if step == -1 and a1 > a0:
          a1 -= 2 * np.pi
        if step == +1 and a1 < a0:
          a1 += 2 * np.pi
        return _interp_crossing(a0, v0, a1, v1, threshold_level)
      i0 = i1
    return None

  theta_left  = find_cross(idx_peak, -1)
  theta_right = find_cross(idx_peak, +1)

  # Normalize back to (-pi, pi]
  theta_peak = angles[idx_peak]
  theta_left  = _unwrap_angle(theta_left)
  theta_right = _unwrap_angle(theta_right)

  # Compute smallest positive span from left to right going through the peak
  span = _unwrap_angle(theta_right - theta_left)
  if span <= 0:
    span += 2 * np.pi

  return theta_left, theta_right, span, theta_peak, False


def calculate_fb_ratio(I, cx, cy, theta_peak, lamb_px, ring_choice_px=None, num_samples=2048):
  """
  Calculate Front-to-Back (F/B) ratio following IEEE antenna measurement standards.

  Args:
    I: 2D intensity array
    cx, cy: center coordinates
    theta_peak: peak direction angle in radians
    lamb_px: wavelength in pixels
    ring_choice_px: optional ring radius in pixels (uses FARFIELD if None)
    num_samples: number of angular samples around the ring

  Returns:
    fb_ratio_db: Front-to-Back ratio in dB, or None if calculation fails
  """
  if theta_peak is None:
    return None

  # Use fixed far-field radius independent of image size for consistent results
  if ring_choice_px is None:
    ring = int(FARFIELD)
  else:
    ring = int(ring_choice_px)

  # Front direction (peak direction)
  front_x = cx + ring * np.cos(theta_peak)
  front_y = cy + ring * np.sin(theta_peak)

  # Back direction (180° opposite to peak)
  theta_back = _unwrap_angle(theta_peak + np.pi)
  back_x = cx + ring * np.cos(theta_back)
  back_y = cy + ring * np.sin(theta_back)

  # Sample intensities at front and back directions
  front_intensity = _bilinear_sample(I, np.array([front_x]), np.array([front_y]))[0]
  back_intensity = _bilinear_sample(I, np.array([back_x]), np.array([back_y]))[0]

  # Calculate F/B ratio in dB
  # Add small epsilon to avoid log(0) and handle very low back radiation
  epsilon = 1e-12
  fb_ratio_linear = front_intensity / (back_intensity + epsilon)
  fb_ratio_db = 10.0 * np.log10(fb_ratio_linear + epsilon)

  return fb_ratio_db


def calculate_directivity_dbi(I, cx, cy, lamb_px, ring_choice_px=None, num_samples=2048):
  """
  Calculate directivity in dBi using peak-to-average intensity ratio on a far-field ring.

  Uses rotational symmetry assumption and compares peak intensity to average intensity
  around the sampling ring to estimate directivity.

  Args:
    I: 2D intensity array
    cx, cy: center coordinates
    lamb_px: wavelength in pixels
    ring_choice_px: optional ring radius in pixels (uses FARFIELD if None)
    num_samples: number of angular samples around the ring

  Returns:
    directivity_dbi: Directivity in dBi, or None if calculation fails
  """
  # Use fixed far-field radius independent of image size for consistent results
  if ring_choice_px is None:
    ring = int(FARFIELD)
  else:
    ring = int(ring_choice_px)

  # Sample intensity around the full ring
  angles = np.linspace(-np.pi, np.pi, num_samples, endpoint=False)
  xs = cx + ring * np.cos(angles)
  ys = cy + ring * np.sin(angles)
  I_theta = _bilinear_sample(I, xs, ys)

  # Find peak intensity
  I_peak = np.max(I_theta)

  # Calculate average intensity around the ring
  I_average = np.mean(I_theta)

  # Calculate directivity: D = I_peak / I_average
  # For a 2D approximation, this gives us the directivity relative to average
  epsilon = 1e-12
  directivity = I_peak / (I_average + epsilon)

  # Convert to dBi
  directivity_dbi = 10.0 * np.log10(directivity + epsilon)

  return directivity_dbi


def load_font_with_weight(size, weight="regular"):
  """Load font with specified weight preference"""
  font_options = {
    "light": [
      "/System/Library/Fonts/Helvetica-Light.ttc",
      "/System/Library/Fonts/Arial.ttf",
      "/usr/share/fonts/truetype/liberation/LiberationSans-Regular.ttf"
    ],
    "regular": [
      "/System/Library/Fonts/Helvetica.ttc",
      "/System/Library/Fonts/Arial.ttf",
      "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf"
    ],
    "bold": [
      "/System/Library/Fonts/Helvetica-Bold.ttc",
      "/System/Library/Fonts/Arial-Bold.ttf",
      "/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf"
    ]
  }

  for font_path in font_options.get(weight, font_options["regular"]):
    try:
      return ImageFont.truetype(font_path, size=size)
    except OSError:
      continue

  return ImageFont.load_default()


def draw_text_enhanced(draw, pos, text, font, fill, outline_color=(0,0,0), letter_spacing=0):
  """Draw text with custom letter spacing and outline"""
  x, y = pos

  if letter_spacing == 0:
    # Standard drawing (faster)
    # Add outline
    for dx in (-1, 1):
      for dy in (-1, 1):
        draw.text((x + dx, y + dy), text, fill=outline_color, font=font)
    draw.text((x, y), text, fill=fill, font=font)
  else:
    # Custom letter spacing
    for char in text:
      # Draw outline
      for dx in (-1, 1):
        for dy in (-1, 1):
          draw.text((x + dx, y + dy), char, fill=outline_color, font=font)
      # Draw character
      draw.text((x, y), char, fill=fill, font=font)
      # Advance position
      char_width = draw.textbbox((0, 0), char, font=font)[2]
      x += char_width + letter_spacing


# ----------------------
# CONFIG
# ----------------------
LAMBDA = 100.00 # wavelength in pixels (same for all sources here)
W, H = int(LAMBDA * 20), int(LAMBDA * 20)
K = 2.0 * np.pi / LAMBDA
FARFIELD = 10 * LAMBDA   # Far-field sampling radius in pixels (independent of image size)
                         # This ensures consistent antenna calculations regardless of output image dimensions
                         # Typical values: 8-15 wavelengths for accurate far-field approximation


# Radiators: name -> dict(x, y, amp, phase)
# - amp: relative amplitude (1.0 = equal power)
# - phase: initial phase offset in degrees (0 by default)
#
# PHYSICAL MEANING OF PHASE RELATIONSHIPS:
# Phase differences between radiators control the interference pattern and beam steering:
# - 0° phase difference: Elements radiate in-phase, creating constructive interference
#   in the direction perpendicular to the array axis (broadside radiation)
# - 90° phase difference: Creates a quadrature relationship, useful for:
#   * Circular polarization when elements are orthogonally positioned
#   * Beam steering and pattern shaping in phased arrays
#   * Reducing mutual coupling between closely spaced elements
# - 180° phase difference: Elements radiate out-of-phase, creating nulls in certain
#   directions and can be used for beam steering or interference cancellation
#
# In this configuration:
# - Element A (center): 0° reference phase
# - Elements B & C (sides): 90° phase lead, creating asymmetric pattern with
#   controlled interference between the three elements

# Three element triangular array (directional)
RADIATORS = {
  "A": {"offset-x": -16.743158, "offset-y": 0, "x": 0, "y": 0, "amp": 1.0, "phase": 0.0},
  "B": {"offset-x": 8.371579, "offset-y": -14.5, "x": 0, "y": 0, "amp": 1.0, "phase": 90.0},
  "C": {"offset-x": 8.371579, "offset-y": 14.5, "x": 0, "y": 0, "amp": 1.0, "phase": 90.0},
}

# Single isotropic radiator (omnidirectional) - uncomment to test
# RADIATORS = {
#   "A": {"offset-x": 0, "offset-y": 0, "x": 0, "y": 0, "amp": 1.0, "phase": 0.0},
# }

# Linear Array
# RADIATORS = {
#   "A": {"offset-x": 0, "offset-y": 0, "x": 0, "y": 0, "amp": 1.0, "phase": 0.0},
#   "B": {"offset-x": LAMBDA, "offset-y": 0, "x": 0, "y": 0, "amp": 1.0, "phase": 0.0},
#   "C": {"offset-x": -LAMBDA, "offset-y": 0, "x": 0, "y": 0, "amp": 1.0, "phase": 0.0},
#   "D": {"offset-x": 2*LAMBDA, "offset-y": 0, "x": 0, "y": 0, "amp": 1.0, "phase": 0.0},
#   "E": {"offset-x": -2*LAMBDA, "offset-y": 0, "x": 0, "y": 0, "amp": 1.0, "phase": 0.0},
#   "F": {"offset-x": 3*LAMBDA, "offset-y": 0, "x": 0, "y": 0, "amp": 1.0, "phase": 0.0},
#   "G": {"offset-x": -3*LAMBDA, "offset-y": 0, "x": 0, "y": 0, "amp": 1.0, "phase": 0.0},
#   "H": {"offset-x": 4*LAMBDA, "offset-y": 0, "x": 0, "y": 0, "amp": 1.0, "phase": 0.0},
#   "I": {"offset-x": -4*LAMBDA, "offset-y": 0, "x": 0, "y": 0, "amp": 1.0, "phase": 0.0},
#   "J": {"offset-x": 5*LAMBDA, "offset-y": 0, "x": 0, "y": 0, "amp": 1.0, "phase": 0.0},
#   "K": {"offset-x": -5*LAMBDA, "offset-y": 0, "x": 0, "y": 0, "amp": 1.0, "phase": 0.0},
# }

# Calculate absolute positions
for name, r in RADIATORS.items():
  r["x"] = W//2 + r["offset-x"]
  r["y"] = H//2 + r["offset-y"]

# Validate radiator positions are within image bounds
validation_errors = []
for name, r in RADIATORS.items():
  x, y = r["x"], r["y"]
  if x < 0 or x >= W:
    validation_errors.append(f"Radiator '{name}' x-position ({x:.1f}) is outside image bounds [0, {W-1}]")
  if y < 0 or y >= H:
    validation_errors.append(f"Radiator '{name}' y-position ({y:.1f}) is outside image bounds [0, {H-1}]")

if validation_errors:
  print("ERROR: Radiator position validation failed:")
  for error in validation_errors:
    print(f"  - {error}")
  print("\nSuggestions:")
  print("  1. Reduce the offset values in the RADIATORS configuration")
  print("  2. Increase the image dimensions (W, H)")
  print("  3. Check that offset-x and offset-y values are reasonable for the image size")
  exit(1)

print("Radiator positions validated successfully:")
for name, r in RADIATORS.items():
  print(f"  {name}: ({r['x']:.1f}, {r['y']:.1f}) - amp={r['amp']}, phase={r['phase']}°")

# Enhanced color mapping with multiple stops for better depth and mid-tone visibility
COLOR_STOPS = [
    (0.0, (0, 0, 20)),        # Very dark blue for near-zero signals
    (0.1, (0, 30, 80)),       # Dark blue for low signals
    (0.3, (0, 70, 140)),      # Medium blue for mid-low signals
    (0.6, (0, 120, 200)),     # Bright blue for mid-high signals
    (0.8, (0, 160, 230)),     # Brighter blue for high signals
    (1.0, (0, 200, 255))      # Bright cyan for maximum signals
]

# Enhanced intensity post-processing options
USE_LOG_SCALE = True      # Use logarithmic scaling to reveal low-level signals
LOG_SCALE_FACTOR = 0.1    # Controls log compression (smaller = more compression)
GAMMA = 1.0               # Additional gamma correction (applied after log scaling)

# HPBW calculation parameters
HPBW_THRESHOLD_DB = 3.0  # Half-power beamwidth threshold in dB below peak
                         # 3.0 dB = half power (0.5x intensity)
                         # 6.0 dB = quarter power (0.25x intensity)
                         # 10.0 dB = one-tenth power (0.1x intensity)

# Omnidirectional detection parameters
OMNI_DETECTION_THRESHOLD = 0.1  # Maximum variation (as fraction of peak) to consider omnidirectional
                                # 0.1 = 10% variation, 0.05 = 5% variation

# Text rendering configuration
TEXT_FONT_SIZE = 20
TEXT_LETTER_SPACING = 1  # Extra pixels between characters (0 = default, 2-3 = more spaced)
TEXT_FONT_WEIGHT = "regular"  # "light", "regular", "bold"

# Draw radiator dots
DRAW_DOTS = True
DOT_RADIUS = 4
DOT_FILL_RGB = (255, 255, 255)  # White dots for radiator positions
DOT_OUTLINE = (0, 0, 0)          # Black outline
DOT_OUTLINE_WIDTH = 2

# Output paths
gradient_path = "interference_gradient.png"
# ----------------------


def _draw_disc_rgba(base_img, center_xy, radius, rgb, alpha=255,
                    outline=None, outline_w=0):
  x, y = center_xy
  d = radius * 2
  disc = Image.new("RGBA", (d, d), (0, 0, 0, 0))
  draw = ImageDraw.Draw(disc)
  draw.ellipse([0, 0, d-1, d-1], fill=(rgb[0], rgb[1], rgb[2], alpha))
  if outline and outline_w > 0:
    draw.ellipse([outline_w/2, outline_w/2, d-1-outline_w/2, d-1-outline_w/2],
                 outline=(*outline, 255), width=outline_w)

  if base_img.mode == "L":
    disc_gray = disc.convert("L")
    disc_alpha = disc.getchannel("A")
    base_img.paste(disc_gray, (x - radius, y - radius), disc_alpha)
  else:
    base_img.paste(disc, (x - radius, y - radius), disc)


# --- Field computation (n sources) ---
y, x = np.mgrid[0:H, 0:W]

# Complex field E = sum_j amp_j * exp(i*(k*r_j + phase_j))
E_real = np.zeros((H, W), dtype=np.float64)
E_imag = np.zeros((H, W), dtype=np.float64)

for r in RADIATORS.values():
  dx = x - r["x"]
  dy = y - r["y"]
  dist = np.hypot(dx, dy)
  # phase_total = K * dist + r.get("phase", 0.0)
  phase_total = K * dist + np.deg2rad(r.get("phase", 0.0))
  amp = r.get("amp", 1.0)
  # Add phasor components
  E_real += amp * np.cos(phase_total)
  E_imag += amp * np.sin(phase_total)

# Intensity I ∝ |E|^2
I = E_real**2 + E_imag**2

# Normalize 0..1 by theoretical max (sum of amplitudes)^2
amp_sum = sum(r.get("amp", 1.0) for r in RADIATORS.values())
I /= (amp_sum ** 2 + 1e-12)

# IMPORTANT: Preserve raw intensity data for antenna calculations (HPBW, F/B, Directivity)
# These calculations must use the original, unprocessed intensity field
I_raw = np.clip(I.copy(), 0.0, 1.0)

# Enhanced intensity processing with logarithmic scaling and gamma correction
# This is ONLY for visualization - antenna calculations use I_raw
I_display = I_raw.copy()

if USE_LOG_SCALE:
  # Apply logarithmic scaling to reveal low-level signals
  # Add small epsilon to avoid log(0), then normalize
  epsilon = LOG_SCALE_FACTOR
  I_original_stats = f"Original intensity - Min: {np.min(I_raw):.6f}, Max: {np.max(I_raw):.6f}, Mean: {np.mean(I_raw):.6f}"

  I_log = np.log10(I_display + epsilon) - np.log10(epsilon)
  I_log_max = np.log10(1.0 + epsilon) - np.log10(epsilon)
  I_display = np.clip(I_log / I_log_max, 0.0, 1.0)

  I_log_stats = f"Log-scaled intensity - Min: {np.min(I_display):.6f}, Max: {np.max(I_display):.6f}, Mean: {np.mean(I_display):.6f}"
  print("Enhanced color mapping applied:")
  print(f"  {I_original_stats}")
  print(f"  {I_log_stats}")
  print(f"  Log scale factor: {LOG_SCALE_FACTOR}")

# Optional additional gamma correction (applied after log scaling)
if GAMMA != 1.0:
  I_display = I_display ** (1.0 / GAMMA)

# --- Build images with enhanced color mapping ---
# Create RGB image using vectorized color stop interpolation
# Use I_display (processed) for visualization, I_raw for antenna calculations
H, W = I_display.shape
rgb = np.zeros((H, W, 3), dtype=np.uint8)

# Vectorized color mapping using numpy operations
I_flat = I_display.flatten()
rgb_flat = np.zeros((len(I_flat), 3))

# For each color channel, interpolate across all pixels at once
for channel in range(3):
  # Extract the channel values from all color stops
  positions = np.array([stop[0] for stop in COLOR_STOPS])
  colors = np.array([stop[1][channel] for stop in COLOR_STOPS])

  # Use numpy's interp function for fast vectorized interpolation
  rgb_flat[:, channel] = np.interp(I_flat, positions, colors)

# Reshape back to image dimensions and convert to uint8
rgb = rgb_flat.reshape(H, W, 3).astype(np.uint8)
img_gradient = Image.fromarray(rgb)

# Draw radiator dots
if DRAW_DOTS:
  for r in RADIATORS.values():
    pos = (int(r["x"]), int(r["y"]))
    _draw_disc_rgba(img_gradient, pos, DOT_RADIUS, DOT_FILL_RGB,
                    alpha=255, outline=DOT_OUTLINE, outline_w=DOT_OUTLINE_WIDTH)

# --- ANTENNA CALCULATIONS: Use extended field if needed for consistent far-field analysis ---
# IMPORTANT: Use I_raw (unprocessed) for accurate antenna calculations
cx, cy = W//2, H//2  # center

# Check if we need to compute an extended field for far-field sampling
required_radius = int(FARFIELD)
max_sampling_distance = required_radius + max(cx, cy, W-cx, H-cy)

if max_sampling_distance > min(W, H) // 2:
  # Need extended field calculation for accurate far-field analysis
  print(f"Computing extended field for far-field analysis (radius: {required_radius} px = {required_radius/LAMBDA:.1f}λ)")
  I_extended, offset_x, offset_y = _compute_extended_field(cx, cy, required_radius, RADIATORS, LAMBDA)

  # Adjust center coordinates for the extended field
  cx_ext = cx + offset_x
  cy_ext = cy + offset_y

  # Use extended field for antenna calculations
  I_analysis = I_extended
  cx_analysis, cy_analysis = cx_ext, cy_ext
else:
  # Original field is sufficient
  I_analysis = I_raw
  cx_analysis, cy_analysis = cx, cy

# --- HPBW: estimate from intensity ring and draw on the image ---
theta_left, theta_right, hpbw_rad, theta_peak, is_omnidirectional = estimate_hpbw(
  I_analysis, cx_analysis, cy_analysis, lamb_px=LAMBDA, ring_choice_px=None, num_samples=2048,
  threshold_db=HPBW_THRESHOLD_DB, omni_threshold=OMNI_DETECTION_THRESHOLD,
)

# --- F/B Ratio: calculate front-to-back ratio ---
fb_ratio_db = None
if not is_omnidirectional and theta_peak is not None:
  fb_ratio_db = calculate_fb_ratio(I_analysis, cx_analysis, cy_analysis, theta_peak, lamb_px=LAMBDA)

# --- Directivity: calculate directivity in dBi ---
directivity_dbi = calculate_directivity_dbi(I_analysis, cx_analysis, cy_analysis, lamb_px=LAMBDA)

draw = ImageDraw.Draw(img_gradient)
line_color = (255, 0, 0)  # Red
line_width = 2

# Handle omnidirectional patterns
if is_omnidirectional:
  print("Omnidirectional pattern detected - uniform radiation in all directions")
  print("No directional beam characteristics to display")
  if directivity_dbi is not None:
    print(f"Directivity: {directivity_dbi:.1f} dBi")

  # Add omnidirectional label at center
  font = load_font_with_weight(TEXT_FONT_SIZE, TEXT_FONT_WEIGHT)

  omni_label = "Omnidirectional"
  # Position label slightly above center
  tx, ty = cx, cy - 30
  draw_text_enhanced(draw, (tx, ty), omni_label, font, (255, 255, 255), letter_spacing=TEXT_LETTER_SPACING)

  # Add directivity label below the omnidirectional label
  if directivity_dbi is not None:
    directivity_label = f"Directivity: {directivity_dbi:.1f} dBi"
    directivity_tx, directivity_ty = cx, cy + 10  # Position below center
    draw_text_enhanced(draw, (directivity_tx, directivity_ty), directivity_label, font, (255, 255, 255), letter_spacing=TEXT_LETTER_SPACING)

else:
  # Directional pattern - draw beam characteristics
  # Make lines long enough to reach edges (Pillow will clip)
  L = max(W, H)

  def _ray_to_point(theta):
    return int(cx + L * math.cos(theta)), int(cy + L * math.sin(theta))

  # Always draw the peak direction line (yellow) if available
  peak_color = (255, 255, 0)  # Yellow
  if theta_peak is not None:
    draw.line([(cx, cy), _ray_to_point(theta_peak)], fill=peak_color, width=line_width)

    # Draw back direction line (semi-transparent yellow) if F/B ratio is available
    if fb_ratio_db is not None:
      theta_back = _unwrap_angle(theta_peak + np.pi)
      back_point = _ray_to_point(theta_back)

      # Create a semi-transparent overlay for the back direction line
      overlay = Image.new('RGBA', img_gradient.size, (0, 0, 0, 0))
      overlay_draw = ImageDraw.Draw(overlay)
      back_color_alpha = (*peak_color, 128)  # 50% transparency (128/255)
      overlay_draw.line([(cx, cy), back_point], fill=back_color_alpha, width=line_width)

      # Composite the overlay onto the main image
      img_gradient = Image.alpha_composite(img_gradient.convert('RGBA'), overlay).convert('RGB')

      # Update the draw object after image conversion
      draw = ImageDraw.Draw(img_gradient)

  # Check if HPBW calculation was successful
  if theta_left is not None and theta_right is not None:
    hpbw_deg = math.degrees(hpbw_rad)

    # Draw the two HPBW boundary rays (red)
    draw.line([ (cx, cy), _ray_to_point(theta_left) ], fill=line_color, width=line_width)
    draw.line([ (cx, cy), _ray_to_point(theta_right) ], fill=line_color, width=line_width)

    # Label: place near the peak direction, offset a bit
    if HPBW_THRESHOLD_DB == 3.0:
      label = f"HPBW: {hpbw_deg:.1f}º"  # Standard -3dB notation
    else:
      label = f"BW(-{HPBW_THRESHOLD_DB:.1f}dB) = {hpbw_deg:.1f}º"  # Show custom threshold

    font = load_font_with_weight(TEXT_FONT_SIZE, TEXT_FONT_WEIGHT)

    # Position the text a bit out along the peak direction, offset downward for breathing room
    label_r = int(0.20 * min(W, H))
    tx = int(cx + label_r * math.cos(theta_peak))
    ty = int(cy + label_r * math.sin(theta_peak))

    # Offset the label downward to give breathing room from the peak line
    ty += 25  # Move label down by 25 pixels

    draw_text_enhanced(draw, (tx, ty), label, font, line_color, letter_spacing=TEXT_LETTER_SPACING)

    print(f"HPBW successfully calculated: {hpbw_deg:.1f}°")
    print(f"Peak direction: {math.degrees(theta_peak):.1f}°")
    if fb_ratio_db is not None:
      print(f"F/B Ratio: {fb_ratio_db:.1f} dB")
    if directivity_dbi is not None:
      print(f"Directivity: {directivity_dbi:.1f} dBi")

      # Add F/B ratio label to image (positioned below HPBW label)
      fb_label = f"F/B Ratio: {fb_ratio_db:.1f} dB"
      fb_label_r = int(0.20 * min(W, H))
      fb_tx = int(cx + fb_label_r * math.cos(theta_peak))
      fb_ty = int(cy + fb_label_r * math.sin(theta_peak))
      fb_ty += 50  # Position below HPBW label (25 + 25 more pixels down)

      draw_text_enhanced(draw, (fb_tx, fb_ty), fb_label, font, line_color, letter_spacing=TEXT_LETTER_SPACING)

    # Add directivity label to image (positioned below F/B label or HPBW if no F/B)
    if directivity_dbi is not None:
      directivity_label = f"Directivity: {directivity_dbi:.1f} dBi"
      directivity_label_r = int(0.20 * min(W, H))
      directivity_tx = int(cx + directivity_label_r * math.cos(theta_peak))
      directivity_ty = int(cy + directivity_label_r * math.sin(theta_peak))
      if fb_ratio_db is not None:
        directivity_ty += 75  # Position below F/B label
      else:
        directivity_ty += 50  # Position below HPBW label

      draw_text_enhanced(draw, (directivity_tx, directivity_ty), directivity_label, font, line_color, letter_spacing=TEXT_LETTER_SPACING)
  else:
    # HPBW calculation failed - provide fallback behavior
    print("Warning: Could not determine HPBW - no clear -3dB crossings found")
    print("This may occur with very broad beams, very narrow beams, or unusual patterns")
    if theta_peak is not None:
      print(f"Peak direction: {math.degrees(theta_peak):.1f}°")
      if fb_ratio_db is not None:
        print(f"F/B Ratio: {fb_ratio_db:.1f} dB")
      if directivity_dbi is not None:
        print(f"Directivity: {directivity_dbi:.1f} dBi")

      # Add warning label
      warning_font = load_font_with_weight(16, TEXT_FONT_WEIGHT)

      warning_label = "HPBW: N/A"
      label_r = int(0.15 * min(W, H))
      tx = int(cx + label_r * math.cos(theta_peak))
      ty = int(cy + label_r * math.sin(theta_peak))

      draw_text_enhanced(draw, (tx, ty), warning_label, warning_font, peak_color, letter_spacing=TEXT_LETTER_SPACING)

# Save
img_gradient.save(gradient_path)

print(gradient_path)
